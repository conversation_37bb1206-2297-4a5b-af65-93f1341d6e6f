import requests
import asyncio
import base64
import logging
import os
import schedule
import time
import subprocess
import threading
from datetime import timedelta

logger = logging.getLogger("alsaniamcp.sentinel")

MEM0_URL = os.getenv("MEM0_URL", "http://localhost:8050")

def is_obfuscated(text):
    try:
        return base64.b64encode(base64.b64decode(text)).decode() == text
    except Exception:
        return False

async def check_memory_integrity():
    """Check memory integrity and quarantine suspicious entries"""
    while True:
        try:
            # Note: This endpoint doesn't exist yet, so we'll skip the actual checks for now
            # but keep the structure for when the endpoint is implemented
            logger.info("🛡️ Running memory integrity check...")

            # TODO: Implement actual memory scanning when /memories endpoint exists
            # res = requests.get(f"{MEM0_URL}/memories?limit=10").json()
            # for memory in res.get("data", []):
            #     text = memory.get("text", "")
            #     entropy = memory.get("entropy", 0)
            #     links = memory.get("links", [])
            #     ttl = memory.get("ttl", "0d")
            #
            #     if entropy > 7.5 and not links:
            #         requests.post(f"{MEM0_URL}/delete", json={"id": memory["id"]})
            #         logger.warning(f"Deleted high-entropy memory: {memory['id']}")
            #
            #     if "d" in ttl and int(ttl.replace("d", "")) > 30 and is_obfuscated(text):
            #         requests.post(f"{MEM0_URL}/quarantine", json={"id": memory["id"]})
            #         logger.warning(f"Quarantined suspicious memory: {memory['id']}")

            await asyncio.sleep(60)  # Check every minute
        except Exception as e:
            logger.error(f"❌ Sentinel check failed: {e}")
            await asyncio.sleep(300)  # Retry in 5 minutes on failure

def run_backup():
    subprocess.run(["python3", "scripts/backup_to_ipfs.py"])

def start_scheduler():
    schedule.every().day.at("03:33").do(run_backup)
    while True:
        schedule.run_pending()
        time.sleep(30)

def start():
    t = threading.Thread(target=start_scheduler, daemon=True)
    t.start()

def start_sentinel():
    """Start sentinel monitoring in background"""
    logger.info("🛡️ Starting Sentinel")
    # Create background task for memory integrity checks
    asyncio.create_task(check_memory_integrity())

if __name__ == "__main__":
    asyncio.run(check_memory_integrity())
