 🧠 AlsaniaMCP Upgrade Roadmap

## ✅ Completed
- Snapshot system with BLAKE3 hash integrity validation
- IPFS snapshot upload/download
- Auto-verify snapshots before restore

---

## 🕒 Next Features

### 1. ⏰ Scheduled Backups + Pruning (scripts/schedule_backup.py)
- Use APScheduler or Cron in FastAPI lifespan
- Create `snapshots/YYYYMMDD-HHMMSS` folder with timestamped backup
- Auto-run `integrity_check.py` before snapshot
- Keep only last N snapshots (e.g., 5)

### 2. 📌 IPFS Pin Validation (scripts/ipfs_validate.py)
- Query IPFS via `nft.storage`, `web3.storage`, or local daemon
- Compare CID with local hash
- Re-pin if missing, alert if corrupt
- Optional: add redundancy via multiple pins (<PERSON>rust, Lighthouse)

### 3. 🧭 Admin Dashboard UI (frontend/mcp/)
- Use same `style.css` global theme
- Display:
  - Current active snapshot
  - Snapshot list + integrity status
  - IPFS status per snapshot
  - Manual upload/download/verify buttons
- Enable/disable autoschedule
- Simple REST hooks via `routes.py`

---

## 🛡 Bonus Security
- Add snapshot signing (ECDSA) with admin key
- Include sig check before restore (scripts/verify_sig.py)