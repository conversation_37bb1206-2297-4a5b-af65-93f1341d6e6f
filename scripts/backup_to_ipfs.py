import subprocess
import datetime
import json
from pathlib import Path

SNAPSHOT_DIR = Path("snapshots")
DB_DUMP_PATH = SNAPSHOT_DIR / "postgres_snapshot.sql"
QDRANT_DUMP_PATH = SNAPSHOT_DIR / "qdrant_snapshot.tar.gz"
LOG_PATH = SNAPSHOT_DIR / "log.json"

SNAPSHOT_DIR.mkdir(exist_ok=True)

def run_backup():
    timestamp = datetime.datetime.utcnow().strftime("%Y-%m-%dT%H-%M-%SZ")

    # Postgres dump
    subprocess.run([
        "pg_dump", "-U", "postgres", "-h", "localhost", "-d", "mem0",
        "-f", str(DB_DUMP_PATH)
    ], check=True)

    # Qdrant dump (optional, placeholder)
    subprocess.run([
        "tar", "-czf", str(QDRANT_DUMP_PATH), "/qdrant/storage"
    ], check=True)

    # IPFS upload
    ipfs_add = subprocess.run(["ipfs", "add", "-r", str(SNAPSHOT_DIR)], capture_output=True, text=True)
    cid = ipfs_add.stdout.strip().split()[-1]

    # Log CID
    entry = {"timestamp": timestamp, "cid": cid}
    if LOG_PATH.exists():
        with open(LOG_PATH, "r") as f:
            log = json.load(f)
    else:
        log = []
    log.append(entry)

    with open(LOG_PATH, "w") as f:
        json.dump(log, f, indent=2)

    print(f"✅ Snapshot backed up to IPFS with CID: {cid}")

if __name__ == "__main__":
    run_backup()