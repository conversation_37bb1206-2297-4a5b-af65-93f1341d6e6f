from fastapi import FastAP<PERSON>, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from fastapi.security import HTT<PERSON><PERSON>earer
from pydantic import BaseModel, field_validator
from src.config import config
from src.embedding.embedder import get_embedding
from src.routes import metrics
from src.memory.vector_store import search_similar, insert_vector
from src.memory.storage import save_memory_entry
from src.memory.forensics import log_access, log_edit
from secure_memory_id import secure_memory_id
from chaos.chaos_mcp import start_chaos_mode
from sentinel.sentinel import start_sentinel
from contextlib import asynccontextmanager
from backup.select_snapshot import select_and_restore_latest_snapshot
import uvicorn
import logging
import asyncio

# Configure logging
logging.basicConfig(level=getattr(logging, config.LOG_LEVEL.upper()))
logger = logging.getLogger("alsaniamcp")

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    logger.info("� Starting Alsania Memory Control Plane")

    # Print configuration
    config.print_config()

    # Validate configuration
    if not config.validate():
        logger.warning("⚠️  Configuration validation failed, some features may not work")

    # Start background services
    if config.ENABLE_SENTINEL:
        logger.info("🛡️ Starting Sentinel")
        start_sentinel()

    if config.ENABLE_CHAOS_MODE:
        logger.info("🔥 Starting Chaos Mode")
        start_chaos_mode()

    logger.info("✅ Application startup complete")
    yield
    logger.info("🛑 Shutting down application")

# === App Config ===
app = FastAPI(
    title="Alsania Memory Control Plane",
    description="Hardened memory server for the Alsania AI ecosystem",
    version="1.0.0",
    lifespan=lifespan
)


# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"]
)

# Include routers
app.include_router(metrics.router)

# Security
security = HTTPBearer()

def verify_token(token: str = Depends(security)):
    """Verify API token"""
    if not token or token.credentials != config.API_TOKEN:
        raise HTTPException(status_code=401, detail="Invalid token")
    return token

# === Models ===
class QueryRequest(BaseModel):
    query: str

    @field_validator('query')
    @classmethod
    def validate_query(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError("Query cannot be empty")
        if len(v) > 10000:
            raise ValueError("Query too long")
        return v.strip()

class StoreRequest(BaseModel):
    text: str
    source: str = "api_user"

    @field_validator('text')
    @classmethod
    def validate_text(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError("Memory text cannot be empty")
        return v.strip()

# === Health Check Routes ===
@app.get("/")
def root():
    """Root endpoint"""
    return {"message": "Echo is alive and hardened.", "version": "1.0.0"}

@app.get("/health")
async def health_check():
    """Comprehensive health check"""
    health_status = {
        "status": "healthy",
        "timestamp": asyncio.get_event_loop().time(),
        "services": {}
    }

    # Check Qdrant connection
    try:
        from src.memory.vector_store import client
        if client:
            client.get_collections()  # Test connection
            health_status["services"]["qdrant"] = "healthy"
        else:
            health_status["services"]["qdrant"] = "unavailable"
            health_status["status"] = "degraded"
    except Exception as e:
        health_status["services"]["qdrant"] = f"error: {str(e)}"
        health_status["status"] = "degraded"

    # Check PostgreSQL connection
    try:
        from src.memory.storage import get_connection_pool
        pool = get_connection_pool()
        conn = pool.getconn()
        pool.putconn(conn)
        health_status["services"]["postgresql"] = "healthy"
    except Exception as e:
        health_status["services"]["postgresql"] = f"error: {str(e)}"
        health_status["status"] = "degraded"

    # Check embedding service
    try:
        if config.OPENROUTER_API_KEY:
            health_status["services"]["embedding"] = "configured"
        else:
            health_status["services"]["embedding"] = "not_configured"
            health_status["status"] = "degraded"
    except Exception as e:
        health_status["services"]["embedding"] = f"error: {str(e)}"
        health_status["status"] = "degraded"

    return health_status

@app.get("/ready")
async def readiness_check():
    """Readiness check for Kubernetes"""
    try:
        # Quick check of essential services
        from src.memory.vector_store import client
        from src.memory.storage import get_connection_pool

        if not client:
            raise Exception("Qdrant client not available")

        pool = get_connection_pool()
        conn = pool.getconn()
        pool.putconn(conn)

        return {"status": "ready"}
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"Service not ready: {str(e)}")

# === API Routes ===

@app.post("/ask")
async def ask(request: QueryRequest, _: str = Depends(verify_token)):
    """Query similar memories using semantic search"""
    try:
        query = request.query
        logger.info(f"📨 Received query: {query}")

        # Generate embedding for the query
        embedding = get_embedding(query)
        if embedding is None:
            raise HTTPException(status_code=500, detail="Failed to generate embedding")

        # Search for similar memories
        results = search_similar(embedding)

        # Generate a memory ID for this query session
        mem_id = secure_memory_id()
        log_access(mem_id, "query")

        return {"results": results, "mem_id": mem_id, "query": query}
    except Exception as e:
        logger.error(f"Error processing query: {e}")
        raise HTTPException(status_code=500, detail="Failed to process query")

@app.post("/store")
async def store_memory(data: StoreRequest, _: str = Depends(verify_token)):
    """Store a memory entry with embedding"""
    try:
        # Generate embedding
        embedding = get_embedding(data.text)
        if embedding is None:
            raise HTTPException(status_code=500, detail="Failed to generate embedding")

        # Generate memory ID and store
        mem_id = secure_memory_id()
        save_memory_entry(mem_id, data.text, data.source)

        # Store vector with payload containing memory metadata
        payload = {
            "memory_id": mem_id,
            "text": data.text,
            "source": data.source
        }
        insert_vector(embedding, payload)

        # Log the operation
        log_edit(mem_id, data.source)

        return {"status": "ok", "mem_id": mem_id}
    except Exception as e:
        logger.error(f"Error storing memory: {e}")
        raise HTTPException(status_code=500, detail="Failed to store memory")

@app.get("/stream")
async def stream_test():
    async def event_generator():
        for i in range(10):
            yield f"data: Echo #{i}\n\n"
            await asyncio.sleep(1)
    return StreamingResponse(event_generator(), media_type="text/event-stream")


# === Main ===
if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=config.HOST,
        port=config.PORT,
        reload=config.RELOAD,
        log_level=config.LOG_LEVEL.lower()
    )