# yaml-language-server: $schema=https://raw.githubusercontent.com/compose-spec/compose-spec/master/schema/compose-spec.json

services:
  mem0:
    image: ghcr.io/mem0ai/openmemory:latest
    ports:
      - "8050:8050"
    environment:
      - QDRANT_URL=http://qdrant:6333
      - POSTGRES_URL=********************************************/mem0
    depends_on:
      qdrant:
        condition: service_healthy
      postgres:
        condition: service_healthy

  qdrant:
    image: qdrant/qdrant:v1.7.0  # Lite version not available; use standard with optimizations :cite[3]
    ports:
      - "6333:6333"
    command: ["--storage", "lite"]  # Enables low-memory mode
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6333/readyz"]
      interval: 10s
      timeout: 5s
      retries: 3

  postgres:
    image: postgres:15-alpine  # Lightweight Alpine variant :cite[6]
    environment:
      POSTGRES_PASSWORD: mem0pass
      POSTGRES_DB: mem0
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5
    volumes:
      - postgres_data:/var/lib/postgresql/data

  ollama:
    image: ollama/ollama:latest
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    command: ["ollama", "serve", "--model", "mistral:7b-instruct-q4"]  # 4-bit quantized for low RAM :cite[9]

volumes:
  postgres_data:
  ollama_data: