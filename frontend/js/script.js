
async function refreshSnapshots() {
  const ul = document.getElementById("snapshots");
  ul.innerHTML = "";
  try {
    const response = await fetch("/snapshots"); // Replace with real backend
    const snapshots = await response.json();
    snapshots.forEach((snap, index) => {
      const li = document.createElement("li");
      li.textContent = `Snapshot #${index + 1}: ${snap.timestamp} [CID: ${snap.cid}]`;
      ul.appendChild(li);
    });
  } catch (err) {
    console.error("Failed to load snapshots", err);
  }
}
