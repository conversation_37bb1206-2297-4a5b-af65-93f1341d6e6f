import hashlib, time, random
import asyncio
import logging
from sklearn.metrics.pairwise import cosine_similarity

logger = logging.getLogger("alsaniamcp.chaos")

def gen_memory_id():
    return hashlib.sha256(str(random.random()).encode()).hexdigest()

def test_uuid_collision():
    ids = {gen_memory_id() for _ in range(1000)}
    assert len(ids) == 1000, "UUID collision detected!"

def test_vector_drift(embedding_fn):
    v1 = embedding_fn("Alsania core contract")
    time.sleep(1)  # Simulate 1d drift in real system
    v2 = embedding_fn("Alsania core contract")
    sim = cosine_similarity([v1], [v2])[0][0]
    assert sim > 0.85, "Vector drift exceeded threshold!"

def test_sleepy_malware(memory):
    if memory.get("ttl", "").endswith("d") and int(memory["ttl"].replace("d", "")) > 30:
        return "trigger_after" in memory
    return False

async def run_chaos_tests():
    """Run chaos tests periodically"""
    while True:
        try:
            logger.info("🔥 Running chaos tests...")
            test_uuid_collision()
            logger.info("✅ UUID collision test passed")

            # Add more chaos tests here as needed
            await asyncio.sleep(3600)  # Run every hour
        except Exception as e:
            logger.error(f"❌ Chaos test failed: {e}")
            await asyncio.sleep(300)  # Retry in 5 minutes on failure

def start_chaos_mode():
    """Start chaos mode - run chaos tests in background"""
    logger.info("🔥 Starting Chaos Mode")
    # Create background task for chaos tests
    asyncio.create_task(run_chaos_tests())
