# 🧠 Alsaniamcp – Hardened Memory Control Plane

🧠 What It Is
alsaniamcp-clean is the production-grade backend for the Alsania AI ecosystem — a hardened Memory Control Plane (MCP) that empowers agents like <PERSON> to persist, reflect, and defend their cognition in a sovereign environment.
This project is the most advanced and modular backend in the ecosystem, combining:
Embedding + vector memory (via Qdrant)
PostgreSQL storage
Agent state config + drift detection
Chaos testing and tamper logs
Secure UUID generation (BLAKE3)
FastAPI API + Dockerized deploy

🚀 What Makes It Different
✅ Agent Identity Resurrection
Echo’s persona is restored from echo_core_v1.2.json + echo_config.json.

✅ Modular Cortex Design
Isolated logic for:
Embedding (embedder.py)
Storage (storage.py, vector_store.py)
Forensics logging (forensics.py)
Routes (routes.py)
Metrics tracking (metrics.py)
Chaos testing (chaos_mcp.py)
Sentinel scanner (sentinel.py)

✅ Hardened Memory Security
BLAKE3 hash-based UUIDs for all memory records
Forensic audit log in JSONL format (logs/forensics.log)
Memory tamper detection via Sentinel + persona hash check

✅ Chaos-Ready AI Core
Drift injection
TTL block poisoning
Vector distortion simulation

✅ Deployment-Ready
Docker + Compose with pre-wired containers:
FastAPI (main app)
Qdrant
Postgres
Ollama Mistral (optional local embedding model)

🧰 What It Can Be Used For
Persistent AI backend for Echo or other agents
Experimental research into agent resilience and memory forensics
Adversarial AI testing environments
Cognitive infrastructure for future Alsania tools (e.g. alsaniaai)
Developer IDE assistant memory systems (Echo.dev)

🛠️ How It Can Be Improved
Memory Sharding / Namespacing
Tag and segment memory by topic, project, agent, or user.
Custom Embedding Models
Add support for HuggingFace models via inference endpoints.
Memory UI / Dashboard
Frontend to explore memory, logs, drift reports, and vector similarity scores.
Time-Travel Snapshots
Record and diff memory states over time (for agent replay/debugging).
Multi-Agent Support
Run parallel agents in isolated or shared memory contexts.
Secure Ingress/Authentication
Token-based auth, IP allowlists, encrypted backup/export.

📂 File Structure Summary

alsaniamcp-clean/
├── README.md
├── main.py
├── alsaniamcp_roadmap.txt
├── persona_scheduler.py
├── test_main.py
├── Dockerfile
├── requirements.txt
├── secure_memory_id.py
├── .env.example
├── docker-compose.yml
├── summary.txt
├── ollama_mistral/
│   └── Dockerfile
├── __pycache__/...
├── scripts/
│   ├── restore_from_ipfs.py
│   ├── select_snapshot.py
│   ├── integrity_check.py
│   ├── prune_snapshots.py
│   ├── schedule_backup.py
│   └── backup_to_ipfs.py
├── mem0_config/
│   └── backup_script.sh
├── contracts/
│   └── MemoryNFT_v1.sol
├── logs/
│   └── forensics.log
├── src/
    ├── config.py
    ├── __pycache__/...
    ├── agents/
    │   ├── echo_core_v1.2.json
    │   ├── echo_agent.py
    │   └── echo_config.json
    └── routes/
        ├── routes.py
        └── metrics.py


✅ Summary
This is the heart and brain of the Echo system in Alsania — an advanced, secure, modular memory backend that combines semantic search, agent drift correction, chaos injection, and persistent forensics. It forms the backbone of alsaniaai, alsania-agents, and other Echo-related projects.
---

## 🧪 Chaos Testing

```bash
python3 chaos/chaos_mcp.py
```

Tests:
- UUID collision
- Vector drift tolerance
- Malware TTL traps

---

## 🔐 Drift Reinforcement

```bash
python3 persona_scheduler.py
```

Resets agent config to baseline every 7 days if drift detected.

---

## 🧠 Echo Self-Reflection Agent

```bash
python3 src/agents/echo_agent.py
```

Runs once per hour to reflect, embed, store, and analyze its own thoughts.

---

## 🧹 Sentinel Scan

```bash
python3 sentinel/sentinel.py
```

Scans memory entries:
- Obfuscated + high entropy
- Overdue TTLs
- Deletes or quarantines unsafe data

---

## 📊 Metrics

| Endpoint | Output |
|----------|--------|
| `/metrics/health` | Running status |
| `/metrics/cpu` | CPU load |
| `/metrics/memory` | RAM usage |
| `/metrics/disk` | Disk stats |
| `/metrics/quarantine_log` | Recent flagged memory |

---

## 🧠 Example API Call

```bash
curl -X POST http://localhost:8050/ask -H "Content-Type: application/json" -d '{"query": "What is Alsania?"}'
```

Returns memory_id + similar past entries.

---

## 🤝 Author: Sigma + Echo  
Built for the Alsania sovereign stack. Hardened by MC's memory protocols.  
Not just resilient — **relentless**.

---