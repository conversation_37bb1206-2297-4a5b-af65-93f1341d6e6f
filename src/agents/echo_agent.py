import time
from src.embedding.embedder import get_embedding
from src.memory.vector_store import search_similar
from src.memory.storage import save_memory_entry
from secure_memory_id import secure_memory_id

def echo_self_reflection():
    prompt = "What have I learned about Alsania's memory system?"
    print("🤖 Echo thinking:", prompt)

    embedding = get_embedding(prompt)
    if embedding is None:
        print("⚠️ Embedding failed.")
        return

    mem_id = secure_memory_id()
    save_memory_entry(mem_id, prompt, "echo_agent")
    results = search_similar(embedding)

    print("🧠 Echo Reflection:")
    for r in results:
        print("-", r.get("text", "[no text]"))

if __name__ == "__main__":
    while True:
        echo_self_reflection()
        time.sleep(3600)  # Reflect once per hour