# mem0_config/routes.py
from fastapi import APIRouter
from pydantic import BaseModel
import subprocess
router = APIRouter(prefix="/api", tags=["Echo Memory"])

# Simple in-memory store for testing
memory_store = []

class Memory(BaseModel):
    text: str
    tag: str = None
    timestamp: str = None

@router.get("/")
def ping():
    return {"message": "Echo memory API is online."}

@router.post("/add")
def add_memory(mem: Memory):
    memory_store.append(mem)
    return {"status": "success", "stored": mem}

@router.get("/all")
def get_memories():
    return memory_store

@router.post("/snapshot")
def trigger_snapshot():
    try:
        result = subprocess.run(["python3", "scripts/backup_to_ipfs.py"], capture_output=True, text=True)
        if result.returncode == 0:
            return {"status": "success", "message": "Snapshot saved to IPFS"}
        else:
            return {"status": "error", "stderr": result.stderr}
    except Exception as e:
        return {"status": "failed", "error": str(e)}