import os
import requests
import time
from tenacity import retry, stop_after_attempt, wait_exponential

@retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
def get_embedding(text):
    api_key = os.getenv("OPENROUTER_API_KEY")
    if not api_key:
        raise ValueError("OPENROUTER_API_KEY not set")
    
    model = os.getenv("OPENROUTER_MODEL", "mistralai/mistral-7b-instruct")
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }

    try:
        res = requests.post("https://openrouter.ai/api/v1/embeddings", headers=headers, json={
            "model": model,
            "input": [text]
        })
        res.raise_for_status()
        return res.json()["data"][0]["embedding"]
    except Exception as e:
        print("Embedding error:", e)
        return None
